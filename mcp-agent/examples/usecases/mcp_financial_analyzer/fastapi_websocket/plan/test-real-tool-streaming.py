#!/usr/bin/env python3
"""
Comprehensive test for real-time tool call streaming with actual VLLM API calls.
This test validates that MCP tool calls are intercepted and streamed in real-time.
"""

import asyncio
import json
import websockets
import requests
from datetime import datetime
from typing import List, Dict, Any


class RealToolStreamingValidator:
    """Validates real-time tool call streaming with actual VLLM and MCP services."""
    
    def __init__(self):
        self.server_url = "ws://localhost:8000"
        self.results = {
            "test_timestamp": datetime.now().isoformat(),
            "tests_run": 0,
            "tests_passed": 0,
            "tool_calls_detected": [],
            "streaming_messages": [],
            "errors": []
        }
    
    async def check_server_health(self):
        """Check if server is running."""
        try:
            response = requests.get("http://localhost:8000/health")
            return response.status_code == 200
        except:
            return False
    
    async def test_research_tool_streaming(self):
        """Test research endpoint with real tool call streaming."""
        print("🔬 Testing research with real tool call streaming...")
        
        endpoint_url = f"{self.server_url}/ws/research/test_user?company=Apple"
        
        try:
            async with websockets.connect(endpoint_url) as websocket:
                # Send a request that should definitely trigger MCP tools
                request = {
                    "message": "Search for Apple Inc. current stock price today and fetch the latest earnings report. I need real-time data from web sources.",
                    "streaming": True
                }
                
                print(f"📤 Sending request: {request['message']}")
                await websocket.send(json.dumps(request))
                
                # Collect streaming messages
                messages = []
                tool_calls = []
                timeout_count = 0
                max_timeout = 90  # 90 second timeout for research with tools
                
                while timeout_count < max_timeout:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        msg_data = json.loads(message)
                        messages.append(msg_data)
                        
                        msg_type = msg_data.get("type", "unknown")
                        
                        if msg_type == "system":
                            print(f"🔔 System: {msg_data.get('message', '')}")
                        elif msg_type == "stream_chunk":
                            content = msg_data.get("message", "")
                            if content.strip() and len(content) > 5:  # Only print meaningful chunks
                                print(f"📦 LLM: {content[:100]}{'...' if len(content) > 100 else ''}")
                        elif msg_type == "mcp_tool_stream":
                            tool_data = msg_data.get("data", {})
                            tool_type = tool_data.get("type", "unknown")
                            tool_name = tool_data.get("tool_name", "unknown")
                            
                            if tool_type == "tool_call_start":
                                args = tool_data.get("args", {})
                                print(f"🔧 TOOL START: {tool_name} - {args}")
                            elif tool_type == "tool_call_result":
                                exec_time = tool_data.get("execution_time", 0)
                                result = tool_data.get("result", {})
                                print(f"✅ TOOL RESULT: {tool_name} - {exec_time:.2f}s - {str(result)[:100]}...")
                            elif tool_type == "tool_call_error":
                                error = tool_data.get("error", "Unknown error")
                                print(f"❌ TOOL ERROR: {tool_name} - {error}")
                            
                            tool_calls.append(tool_data)
                            self.results["tool_calls_detected"].append(tool_data)
                            
                        elif msg_type == "stream_end":
                            print(f"🏁 Stream ended: {msg_data.get('message', '')}")
                            break
                        elif msg_type == "error":
                            print(f"❌ Error: {msg_data.get('message', '')}")
                            self.results["errors"].append(msg_data)
                            break
                            
                    except asyncio.TimeoutError:
                        timeout_count += 1
                        if timeout_count % 15 == 0:  # Print every 15 seconds
                            print(f"⏳ Waiting for tool calls... ({timeout_count}s)")
                        continue
                    except websockets.exceptions.ConnectionClosed:
                        print("🔌 WebSocket connection closed")
                        break
                    except Exception as e:
                        print(f"❌ Error receiving message: {e}")
                        break
                
                self.results["streaming_messages"] = messages
                return len(tool_calls) > 0, len(messages) > 0
                
        except Exception as e:
            print(f"❌ Research tool streaming test failed: {e}")
            self.results["errors"].append({"test": "research_tool_streaming", "error": str(e)})
            return False, False
    
    async def analyze_tool_streaming_results(self):
        """Analyze the tool streaming results."""
        print("\n" + "=" * 60)
        print("📊 TOOL STREAMING ANALYSIS")
        print("=" * 60)
        
        total_messages = len(self.results["streaming_messages"])
        total_tool_calls = len(self.results["tool_calls_detected"])
        
        print(f"Total messages received: {total_messages}")
        print(f"Total tool calls detected: {total_tool_calls}")
        
        if total_tool_calls > 0:
            print(f"\n🔧 Tool Call Breakdown:")
            
            # Analyze tool call types
            tool_types = {}
            tool_names = set()
            
            for tool_call in self.results["tool_calls_detected"]:
                call_type = tool_call.get("type", "unknown")
                tool_name = tool_call.get("tool_name", "unknown")
                
                tool_types[call_type] = tool_types.get(call_type, 0) + 1
                tool_names.add(tool_name)
            
            for call_type, count in tool_types.items():
                print(f"   {call_type}: {count}")
            
            print(f"\n🛠️ Tools used: {', '.join(tool_names)}")
            
            # Check for expected tools
            has_search = any("search" in name.lower() for name in tool_names)
            has_fetch = any("fetch" in name.lower() for name in tool_names)
            
            print(f"\n✅ Google Search detected: {has_search}")
            print(f"✅ Web Fetch detected: {has_fetch}")
            
            # Analyze tool execution flow
            starts = sum(1 for tc in self.results["tool_calls_detected"] if tc.get("type") == "tool_call_start")
            results = sum(1 for tc in self.results["tool_calls_detected"] if tc.get("type") == "tool_call_result")
            errors = sum(1 for tc in self.results["tool_calls_detected"] if tc.get("type") == "tool_call_error")
            
            print(f"\n📈 Execution Flow:")
            print(f"   Tool starts: {starts}")
            print(f"   Tool results: {results}")
            print(f"   Tool errors: {errors}")
            
            success_rate = (results / starts * 100) if starts > 0 else 0
            print(f"   Success rate: {success_rate:.1f}%")
            
            return total_tool_calls > 0 and starts > 0 and results > 0
        else:
            print("\n⚠️ No tool calls detected")
            return False
    
    async def run_validation_tests(self):
        """Run all validation tests."""
        print("🚀 Starting Real-Time Tool Call Streaming Validation")
        print("=" * 60)
        
        # Check server health
        if not await self.check_server_health():
            print("❌ Server not available")
            return False
        
        print("✅ Server is running")
        
        # Test research tool streaming
        self.results["tests_run"] += 1
        has_tool_calls, has_messages = await self.test_research_tool_streaming()
        
        if has_tool_calls and has_messages:
            self.results["tests_passed"] += 1
            print("✅ Research tool streaming test passed")
        else:
            print("❌ Research tool streaming test failed")
        
        # Analyze results
        tool_streaming_working = await self.analyze_tool_streaming_results()
        
        # Final assessment
        print("\n" + "=" * 60)
        print("🎯 FINAL ASSESSMENT")
        print("=" * 60)
        
        if tool_streaming_working:
            print("🎉 SUCCESS: Real-time tool call streaming is working!")
            print("✅ MCP tools are being intercepted and streamed")
            print("✅ Tool parameters and results are captured")
            print("✅ Real-time visibility into research process achieved")
        else:
            print("⚠️ PARTIAL SUCCESS: Basic streaming works but tool interception needs work")
            print("📝 Next steps:")
            print("   1. Verify MCP services are properly configured")
            print("   2. Check that research queries trigger actual tool calls")
            print("   3. Ensure tool call interception is working in VLLMAugmentedLLM")
        
        # Save results
        results_file = f"tool_streaming_validation_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        print(f"\n💾 Results saved to: {results_file}")
        
        return tool_streaming_working


async def main():
    """Run the tool streaming validation tests."""
    validator = RealToolStreamingValidator()
    success = await validator.run_validation_tests()
    
    if success:
        print("\n🎉 All tests passed! Tool call streaming is working!")
    else:
        print("\n⚠️ Tests completed with issues - check results for details")


if __name__ == "__main__":
    asyncio.run(main())
