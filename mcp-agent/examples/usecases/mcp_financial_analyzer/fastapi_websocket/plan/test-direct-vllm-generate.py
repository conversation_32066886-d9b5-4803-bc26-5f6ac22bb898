#!/usr/bin/env python3
"""
Test the VLLMAugmentedLLM.generate method directly to verify tool calling.
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from session_manager import FinancialSession, SessionType


async def test_direct_vllm_generate():
    """Test VLLMAugmentedLLM.generate method directly."""
    
    print("🔧 Testing VLLMAugmentedLLM.generate method directly...")
    
    # Collect streaming messages
    streaming_messages = []
    
    async def test_stream_callback(message):
        """Collect streaming messages for testing."""
        streaming_messages.append(message)
        print(f"🔧 Tool Stream: {message.get('type')} - {message.get('tool_name', 'unknown')}")
    
    try:
        # Create a research session
        session = FinancialSession("direct_test_user", "direct_test_session", SessionType.RESEARCH)
        print("✅ Session object created")
        
        # Initialize session
        print("🔄 Initializing session...")
        await session.initialize("Apple Inc.")
        print("✅ Session initialized successfully")
        
        # Set the streaming callback
        session.set_mcp_stream_callback(test_stream_callback)
        print("✅ Streaming callback set")
        
        # Test 1: Direct VLLMAugmentedLLM.generate call
        print("\n1️⃣ Testing direct VLLMAugmentedLLM.generate call...")
        
        try:
            # Call the generate method directly
            message = "Search for Apple Inc. current stock price using available tools"
            
            print(f"📤 Calling VLLMAugmentedLLM.generate with: {message}")
            
            # This should trigger tool calling if configured correctly
            responses = await session.llm.generate(
                message=message,
                enable_thinking=True
            )
            
            print(f"✅ Generate call completed")
            print(f"📥 Number of responses: {len(responses)}")
            
            for i, response in enumerate(responses):
                print(f"📄 Response {i+1}:")
                print(f"   Content: {str(response.content)[:200]}...")
                if hasattr(response, 'tool_calls') and response.tool_calls:
                    print(f"   Tool calls: {len(response.tool_calls)}")
                    for tool_call in response.tool_calls:
                        print(f"     - {tool_call.function.name}: {tool_call.function.arguments}")
                else:
                    print(f"   No tool calls in response")
            
        except Exception as e:
            print(f"❌ Direct generate call failed: {e}")
            import traceback
            traceback.print_exc()
        
        # Test 2: Check if tools are being passed to the VLLM API
        print("\n2️⃣ Testing tool availability in VLLMAugmentedLLM...")
        
        try:
            # Check if the agent has tools
            tools_response = await session.research_agent.list_tools()
            if hasattr(tools_response, 'tools') and tools_response.tools:
                print(f"✅ Agent has {len(tools_response.tools)} tools available")
                
                # Format tools as OpenAI format (same as VLLMAugmentedLLM does)
                available_tools = [
                    {
                        "type": "function",
                        "function": {
                            "name": tool.name,
                            "description": tool.description,
                            "parameters": tool.inputSchema,
                        },
                    }
                    for tool in tools_response.tools
                ]
                
                print(f"📋 Formatted tools for VLLM:")
                for tool in available_tools:
                    func = tool["function"]
                    print(f"   - {func['name']}: {func['description']}")
                
                # Test 3: Direct OpenAI client call with these tools
                print("\n3️⃣ Testing direct OpenAI client with formatted tools...")
                
                from openai import OpenAI
                
                client = OpenAI(
                    api_key="EMPTY",
                    base_url="http://************:38701/v1"
                )
                
                messages = [
                    {
                        "role": "user",
                        "content": "Search for Apple Inc. current stock price using the g-search_search tool"
                    }
                ]
                
                response = client.chat.completions.create(
                    model="Qwen/Qwen3-32B",
                    messages=messages,
                    tools=available_tools,
                    tool_choice="auto",
                    extra_body={
                        "chat_template_kwargs": {"enable_thinking": True}
                    }
                )
                
                if response.choices[0].message.tool_calls:
                    print(f"✅ Direct OpenAI client tool call successful!")
                    for tool_call in response.choices[0].message.tool_calls:
                        print(f"   Function: {tool_call.function.name}")
                        print(f"   Arguments: {tool_call.function.arguments}")
                else:
                    print(f"❌ Direct OpenAI client did not generate tool calls")
                    print(f"   Response: {response.choices[0].message.content}")
                
            else:
                print(f"❌ No tools available from agent")
                
        except Exception as e:
            print(f"❌ Tool availability test failed: {e}")
            import traceback
            traceback.print_exc()
        
        # Analyze streaming results
        print(f"\n📊 Streaming Analysis:")
        print(f"   Total streaming messages: {len(streaming_messages)}")
        
        if streaming_messages:
            message_types = {}
            for msg in streaming_messages:
                msg_type = msg.get('type', 'unknown')
                message_types[msg_type] = message_types.get(msg_type, 0) + 1
            
            print(f"   Message types: {message_types}")
            return len(streaming_messages) > 0
        else:
            print(f"   ❌ No streaming messages received")
            return False
        
    except Exception as e:
        print(f"❌ Direct VLLMAugmentedLLM test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        try:
            await session.cleanup()
            print("✅ Session cleaned up")
        except:
            pass


async def main():
    """Run the direct VLLMAugmentedLLM test."""
    print("🚀 Direct VLLMAugmentedLLM Generate Test")
    print("=" * 60)
    
    success = await test_direct_vllm_generate()
    
    if success:
        print("\n🎉 Direct VLLMAugmentedLLM test completed!")
    else:
        print("\n❌ Direct VLLMAugmentedLLM test failed!")
    
    return success


if __name__ == "__main__":
    asyncio.run(main())
