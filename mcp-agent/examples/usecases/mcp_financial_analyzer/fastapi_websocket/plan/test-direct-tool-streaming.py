#!/usr/bin/env python3
"""
Test direct tool call streaming to verify our interception works.
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from session_manager import FinancialSession, SessionType


async def test_direct_tool_streaming():
    """Test direct tool call with streaming interception."""
    
    print("🔧 Testing direct tool call streaming...")
    
    # Collect streaming messages
    streaming_messages = []
    
    async def test_stream_callback(message):
        """Collect streaming messages for testing."""
        streaming_messages.append(message)
        print(f"🔧 Tool Stream: {message.get('type')} - {message.get('tool_name', 'unknown')}")
        if message.get('type') == 'tool_call_start':
            print(f"   Args: {message.get('args', {})}")
        elif message.get('type') == 'tool_call_result':
            result = message.get('result', {})
            print(f"   Result: {str(result)[:100]}...")
        elif message.get('type') == 'tool_call_error':
            print(f"   Error: {message.get('error', 'Unknown error')}")
    
    try:
        # Create a research session
        session = FinancialSession("stream_test_user", "stream_test_session", SessionType.RESEARCH)
        print("✅ Session object created")
        
        # Initialize session
        print("🔄 Initializing session...")
        await session.initialize("Apple Inc.")
        print("✅ Session initialized successfully")
        
        # Set the streaming callback
        session.set_mcp_stream_callback(test_stream_callback)
        print("✅ Streaming callback set")
        
        # Test direct tool call through the LLM
        print("\n🔧 Testing direct tool call through StreamingVLLMAugmentedLLM...")
        
        try:
            # Create a CallToolRequest for the correct tool name
            from mcp.types import CallToolRequest, CallToolRequestParams
            
            # Use the correct tool name from our previous test
            tool_request = CallToolRequest(
                method="tools/call",
                params=CallToolRequestParams(
                    name="g-search_search",
                    arguments={"query": "Apple Inc. stock price test"}
                )
            )
            
            print(f"📤 Calling tool: {tool_request.params.name}")
            print(f"📤 With args: {tool_request.params.arguments}")
            
            # Call the tool through our streaming LLM
            result = await session.llm.call_tool(tool_request, tool_call_id="test_call_1")
            
            print(f"✅ Tool call completed")
            print(f"📥 Result type: {type(result)}")
            print(f"📥 Result preview: {str(result)[:200]}...")
            
        except Exception as e:
            print(f"❌ Direct tool call failed: {e}")
            import traceback
            traceback.print_exc()
        
        # Test fetch tool as well
        print("\n🌐 Testing fetch tool...")
        
        try:
            fetch_request = CallToolRequest(
                method="tools/call",
                params=CallToolRequestParams(
                    name="fetch_fetch",
                    arguments={"url": "https://httpbin.org/json"}
                )
            )
            
            print(f"📤 Calling tool: {fetch_request.params.name}")
            result = await session.llm.call_tool(fetch_request, tool_call_id="test_call_2")
            
            print(f"✅ Fetch tool call completed")
            print(f"📥 Result preview: {str(result)[:200]}...")
            
        except Exception as e:
            print(f"❌ Fetch tool call failed: {e}")
        
        # Analyze streaming results
        print(f"\n📊 Streaming Analysis:")
        print(f"   Total streaming messages: {len(streaming_messages)}")
        
        if streaming_messages:
            message_types = {}
            for msg in streaming_messages:
                msg_type = msg.get('type', 'unknown')
                message_types[msg_type] = message_types.get(msg_type, 0) + 1
            
            print(f"   Message types: {message_types}")
            
            # Check for complete tool call flow
            has_start = any(msg.get('type') == 'tool_call_start' for msg in streaming_messages)
            has_result = any(msg.get('type') == 'tool_call_result' for msg in streaming_messages)
            has_error = any(msg.get('type') == 'tool_call_error' for msg in streaming_messages)
            
            print(f"   ✅ Tool call start: {has_start}")
            print(f"   ✅ Tool call result: {has_result}")
            print(f"   ⚠️ Tool call error: {has_error}")
            
            if has_start and (has_result or has_error):
                print(f"\n🎉 SUCCESS: Tool call streaming is working!")
                return True
            else:
                print(f"\n⚠️ PARTIAL: Some streaming messages missing")
                return False
        else:
            print(f"   ❌ No streaming messages received")
            return False
        
    except Exception as e:
        print(f"❌ Direct tool streaming test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        try:
            await session.cleanup()
            print("✅ Session cleaned up")
        except:
            pass


async def main():
    """Run the direct tool streaming test."""
    print("🚀 Direct Tool Call Streaming Test")
    print("=" * 50)
    
    success = await test_direct_tool_streaming()
    
    if success:
        print("\n🎉 Direct tool streaming test passed!")
    else:
        print("\n❌ Direct tool streaming test failed!")
    
    return success


if __name__ == "__main__":
    asyncio.run(main())
