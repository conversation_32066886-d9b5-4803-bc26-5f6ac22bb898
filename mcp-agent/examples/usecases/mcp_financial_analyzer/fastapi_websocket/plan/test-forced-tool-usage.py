#!/usr/bin/env python3
"""
Test to force tool usage by explicitly instructing the LLM to use tools.
"""

import asyncio
import json
import websockets
import requests


async def test_forced_tool_usage():
    """Test WebSocket streaming with explicit tool usage instructions."""
    
    # Check server health
    try:
        response = requests.get("http://localhost:8000/health")
        print(f"✅ Server health: {response.status_code}")
    except Exception as e:
        print(f"❌ Server not available: {e}")
        return False
    
    # Test WebSocket connection with explicit tool usage instructions
    endpoint_url = "ws://localhost:8000/ws/research/test_user?company=Apple"
    
    print(f"🔗 Connecting to: {endpoint_url}")
    
    try:
        async with websockets.connect(endpoint_url) as websocket:
            print("✅ WebSocket connected successfully!")
            
            # Send a request that explicitly forces tool usage
            request = {
                "message": """You MUST use the available tools to search for real-time information. 
                
                STEP 1: Use the g-search_search tool to search for "Apple Inc stock price today"
                STEP 2: Use the fetch_fetch tool to fetch content from a financial website
                
                Do NOT generate responses from your training data. You MUST call the tools first.
                
                Available tools:
                - g-search_search: Use this to search Google for current information
                - fetch_fetch: Use this to fetch content from websites
                
                Execute the tool calls now and provide the real search results.""",
                "streaming": True
            }
            
            print(f"📤 Sending explicit tool usage request...")
            await websocket.send(json.dumps(request))
            
            # Collect messages and look for tool usage
            messages = []
            tool_calls_detected = []
            timeout_count = 0
            max_timeout = 90  # 90 second timeout
            
            while timeout_count < max_timeout:
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                    msg_data = json.loads(message)
                    messages.append(msg_data)
                    
                    msg_type = msg_data.get("type", "unknown")
                    
                    if msg_type == "system":
                        print(f"🔔 System: {msg_data.get('message', '')}")
                    elif msg_type == "stream_chunk":
                        content = msg_data.get("message", "")
                        if content.strip() and len(content) > 3:
                            # Look for tool usage indicators
                            if any(keyword in content.lower() for keyword in [
                                "search", "tool", "calling", "fetch", "using", "execute"
                            ]):
                                print(f"🔍 Tool indicator: {content[:100]}...")
                            else:
                                print(f"📦 LLM: {content[:80]}{'...' if len(content) > 80 else ''}")
                    elif msg_type == "mcp_tool_stream":
                        tool_data = msg_data.get("data", {})
                        tool_type = tool_data.get("type", "unknown")
                        tool_name = tool_data.get("tool_name", "unknown")
                        
                        if tool_type == "tool_call_start":
                            args = tool_data.get("args", {})
                            print(f"🔧 TOOL START: {tool_name} - {args}")
                        elif tool_type == "tool_call_result":
                            exec_time = tool_data.get("execution_time", 0)
                            result = tool_data.get("result", {})
                            print(f"✅ TOOL RESULT: {tool_name} - {exec_time:.2f}s")
                        elif tool_type == "tool_call_error":
                            error = tool_data.get("error", "Unknown error")
                            print(f"❌ TOOL ERROR: {tool_name} - {error}")
                        
                        tool_calls_detected.append(tool_data)
                        
                    elif msg_type == "stream_end":
                        print(f"🏁 Stream ended: {msg_data.get('message', '')}")
                        break
                    elif msg_type == "error":
                        print(f"❌ Error: {msg_data.get('message', '')}")
                        break
                        
                except asyncio.TimeoutError:
                    timeout_count += 1
                    if timeout_count % 15 == 0:  # Print every 15 seconds
                        print(f"⏳ Waiting for tool usage... ({timeout_count}s)")
                    continue
                except websockets.exceptions.ConnectionClosed:
                    print("🔌 WebSocket connection closed")
                    break
                except Exception as e:
                    print(f"❌ Error receiving message: {e}")
                    break
            
            print(f"\n📊 Results Analysis:")
            print(f"   Total messages: {len(messages)}")
            print(f"   Tool calls detected: {len(tool_calls_detected)}")
            
            if tool_calls_detected:
                print(f"\n🎉 SUCCESS: Tool calls were detected!")
                
                # Analyze tool calls
                tool_names = set()
                for tool_call in tool_calls_detected:
                    tool_name = tool_call.get("tool_name", "unknown")
                    tool_names.add(tool_name)
                
                print(f"   Tools used: {', '.join(tool_names)}")
                return True
            else:
                print(f"\n⚠️ No tool calls detected")
                
                # Analyze content for clues
                all_content = ""
                for msg in messages:
                    if msg.get("type") == "stream_chunk":
                        all_content += msg.get("message", "")
                
                # Look for indicators that the LLM is trying to use tools
                tool_mentions = [
                    "tool", "search", "fetch", "call", "execute", "using", "available"
                ]
                
                mentions_found = []
                for mention in tool_mentions:
                    if mention in all_content.lower():
                        mentions_found.append(mention)
                
                print(f"   Tool mentions in response: {mentions_found}")
                
                # Check if the LLM is acknowledging the instruction
                if "tool" in all_content.lower() or "search" in all_content.lower():
                    print(f"   🔍 LLM acknowledges tools but may not be calling them")
                    print(f"   💡 This suggests the model understands but isn't configured for tool calling")
                else:
                    print(f"   ❌ LLM doesn't seem to understand tool instructions")
                
                return False
                
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")
        return False


async def main():
    """Run the forced tool usage test."""
    print("🚀 Testing Forced Tool Usage")
    print("=" * 50)
    
    success = await test_forced_tool_usage()
    
    if success:
        print("\n🎉 Forced tool usage test passed!")
        print("✅ The LLM is successfully calling MCP tools")
    else:
        print("\n⚠️ Forced tool usage test failed!")
        print("\n📝 Possible issues:")
        print("1. VLLM model not configured for tool calling")
        print("2. Tool calling not enabled in the LLM configuration")
        print("3. Model doesn't understand tool calling instructions")
        print("4. Tool calling requires specific prompt format")
    
    return success


if __name__ == "__main__":
    asyncio.run(main())
