import asyncio
import os
import uuid
import json
from typing import Dict, Optional, AsyncGenerator, Callable, Any
from datetime import datetime
from enum import Enum

from mcp_agent.app import <PERSON><PERSON><PERSON>
from mcp_agent.agents.agent import Agent
from mcp_agent.workflows.llm.augmented_llm_openai import OpenAIAugmentedLLM
from mcp_agent.workflows.llm.augmented_llm_vllm import VLLMAugmentedLL<PERSON>
from mcp_agent.workflows.llm.augmented_llm import CallToolRequest
from openai.types.chat import ChatCompletionMessageToolCall
from mcp_agent.workflows.orchestrator.orchestrator import Orchestrator
from mcp_agent.workflows.llm.augmented_llm import RequestParams
from mcp_agent.workflows.evaluator_optimizer.evaluator_optimizer import (
    EvaluatorOptimizerLLM,
    QualityRating,
)

# Import agent factories from parent directory
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from agents.research_agent import create_research_agent
from agents.analyst_agent import create_analyst_agent
from agents.report_writer import create_report_writer


class SessionType(Enum):
    """Types of financial analysis sessions."""
    RESEARCH = "research"
    ANALYZE = "analyze"
    REPORT = "report"
    FULL_ANALYSIS = "full_analysis"


class StreamingOpenAILLM:
    """Wrapper for OpenAIAugmentedLLM that provides streaming capabilities."""

    def __init__(self, llm: OpenAIAugmentedLLM):
        self.llm = llm

    async def generate_str_streaming(
        self,
        message: str,
        request_params: RequestParams | None = None,
        stream_callback: Callable[[str], None] | None = None
    ) -> str:
        """Generate a streaming response using OpenAI's streaming API."""
        try:
            # For now, let's use a simple approach that simulates streaming
            # by calling the regular LLM and then streaming the response word by word

            if stream_callback:
                await stream_callback("Starting analysis...\n\n")

            # Get the full response first
            full_response = await self.llm.generate_str(message, request_params)

            # Stream the response word by word to simulate real streaming
            if stream_callback and full_response:
                words = full_response.split()
                current_chunk = ""

                for i, word in enumerate(words):
                    current_chunk += word + " "

                    # Send chunks of 3-5 words at a time
                    if (i + 1) % 4 == 0 or i == len(words) - 1:
                        chunk_to_send = " ".join(words[max(0, i-3):i+1]) + " "
                        await stream_callback(chunk_to_send)
                        # Small delay to simulate real streaming
                        await asyncio.sleep(0.1)

            return full_response

        except Exception as e:
            # Fallback to non-streaming if streaming fails
            error_msg = f"[Streaming failed, using fallback: {str(e)}]\n"
            if stream_callback:
                await stream_callback(error_msg)
            return await self.llm.generate_str(message, request_params)


class StreamingVLLMLLM:
    """Wrapper for VLLMAugmentedLLM that provides real streaming capabilities."""

    def __init__(self, llm: VLLMAugmentedLLM, api_base: str = None, api_key: str = None, mcp_streaming_wrapper=None):
        self.llm = llm
        self.mcp_streaming_wrapper = mcp_streaming_wrapper

        # Handle both real LLM objects and mocks
        if hasattr(llm, 'vllm_api_base'):
            default_api_base = getattr(llm, 'vllm_api_base', 'http://192.168.1.54:38701/v1')
        else:
            default_api_base = 'http://192.168.1.54:38701/v1'

        if hasattr(llm, 'vllm_api_key'):
            default_api_key = getattr(llm, 'vllm_api_key', 'EMPTY')
        else:
            default_api_key = 'EMPTY'

        # Ensure we have string values, not Mock objects
        self.api_base = api_base or (default_api_base if isinstance(default_api_base, str) else 'http://192.168.1.54:38701/v1')
        self.api_key = api_key or (default_api_key if isinstance(default_api_key, str) else 'EMPTY')

        # Create OpenAI client for streaming
        from openai import OpenAI
        try:
            self.client = OpenAI(
                api_key=self.api_key,
                base_url=self.api_base
            )
        except Exception as e:
            # For testing, create a mock client
            # from unittest.mock import Mock
            # self.client = Mock()
            # self.client.chat = Mock()
            # self.client.chat.completions = Mock()
            # self.client.chat.completions.create = Mock()

    async def generate_str_streaming(
        self,
        message: str,
        request_params: RequestParams | None = None,
        stream_callback: Callable[[str], None] | None = None,
        model: str = None,
        **kwargs
    ) -> str:
        """Generate a streaming response using VLLMAugmentedLLM with tool calling support."""
        try:
            if stream_callback:
                await stream_callback("Initializing VLLM analysis...\n\n")

            # Use VLLMAugmentedLLM.generate method to get proper tool calling support
            # This method handles tool retrieval, formatting, and execution
            responses = await self.llm.generate(
                message=message,
                request_params=request_params,
                enable_thinking=True
            )

            # Extract content from responses and stream it
            full_response = ""

            for response in responses:
                content = response.content
                if not content:
                    continue

                if isinstance(content, str):
                    full_response += content
                    if stream_callback:
                        # Stream the content in chunks for better UX
                        words = content.split()
                        for i in range(0, len(words), 3):  # 3 words per chunk
                            chunk = " ".join(words[i:i+3]) + " "
                            await stream_callback(chunk)
                            await asyncio.sleep(0.02)  # Small delay for streaming effect

            return full_response

        except Exception as e:
            # Handle errors gracefully
            error_msg = f"Error processing financial analysis: {str(e)}"
            if stream_callback:
                await stream_callback(error_msg)
            return error_msg

    async def _async_stream_wrapper(self, stream):
        """Convert synchronous stream to async generator."""
        import asyncio
        import concurrent.futures

        def get_next_chunk():
            try:
                return next(stream)
            except StopIteration:
                return None

        with concurrent.futures.ThreadPoolExecutor() as executor:
            while True:
                try:
                    chunk = await asyncio.get_event_loop().run_in_executor(
                        executor, get_next_chunk
                    )
                    if chunk is None:
                        break
                    yield chunk
                except Exception:
                    break


class StreamingVLLMAugmentedLLM(VLLMAugmentedLLM):
    """Custom VLLMAugmentedLLM that intercepts and streams tool calls in real-time."""

    def __init__(self, *args, tool_stream_callback=None, **kwargs):
        """Initialize with optional tool streaming callback."""
        super().__init__(*args, **kwargs)
        self.tool_stream_callback = tool_stream_callback

    async def select_model(self, request_params=None):
        """Override model selection to ensure we use the VLLM model."""
        # Always return the VLLM model
        return "Qwen/Qwen3-32B"

    async def call_tool(
        self,
        request: CallToolRequest,
        tool_call_id: str | None = None,
    ):
        """Override call_tool to intercept and stream tool calls."""
        tool_name = request.params.name
        tool_args = request.params.arguments

        # Stream tool call start
        if self.tool_stream_callback:
            await self._stream_tool_event({
                "type": "tool_call_start",
                "tool_name": tool_name,
                "args": tool_args,
                "tool_call_id": tool_call_id,
                "timestamp": datetime.now().isoformat()
            })

        start_time = datetime.now()

        try:
            # Execute the actual tool call
            result = await super().call_tool(request, tool_call_id)

            execution_time = (datetime.now() - start_time).total_seconds()

            # Stream tool call result
            if self.tool_stream_callback:
                await self._stream_tool_event({
                    "type": "tool_call_result",
                    "tool_name": tool_name,
                    "result": self._serialize_tool_result(result),
                    "execution_time": execution_time,
                    "tool_call_id": tool_call_id,
                    "timestamp": datetime.now().isoformat()
                })

            return result

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()

            # Stream tool call error
            if self.tool_stream_callback:
                await self._stream_tool_event({
                    "type": "tool_call_error",
                    "tool_name": tool_name,
                    "error": str(e),
                    "execution_time": execution_time,
                    "tool_call_id": tool_call_id,
                    "timestamp": datetime.now().isoformat()
                })

            raise

    async def execute_tool_call(self, tool_call):
        """Override execute_tool_call to intercept tool calls at the execution level."""
        tool_name = tool_call.function.name
        tool_args_str = tool_call.function.arguments
        tool_call_id = tool_call.id

        # Parse tool arguments
        tool_args = {}
        try:
            if tool_args_str:
                import json
                tool_args = json.loads(tool_args_str)
        except json.JSONDecodeError:
            tool_args = {"raw_arguments": tool_args_str}

        # Stream tool call start
        if self.tool_stream_callback:
            await self._stream_tool_event({
                "type": "tool_call_start",
                "tool_name": tool_name,
                "args": tool_args,
                "tool_call_id": tool_call_id,
                "timestamp": datetime.now().isoformat()
            })

        start_time = datetime.now()

        try:
            # Execute the actual tool call via parent method
            result = await super().execute_tool_call(tool_call)

            execution_time = (datetime.now() - start_time).total_seconds()

            # Stream tool call result
            if self.tool_stream_callback:
                await self._stream_tool_event({
                    "type": "tool_call_result",
                    "tool_name": tool_name,
                    "result": self._serialize_tool_message(result),
                    "execution_time": execution_time,
                    "tool_call_id": tool_call_id,
                    "timestamp": datetime.now().isoformat()
                })

            return result

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()

            # Stream tool call error
            if self.tool_stream_callback:
                await self._stream_tool_event({
                    "type": "tool_call_error",
                    "tool_name": tool_name,
                    "error": str(e),
                    "execution_time": execution_time,
                    "tool_call_id": tool_call_id,
                    "timestamp": datetime.now().isoformat()
                })

            raise

    async def _stream_tool_event(self, event_data):
        """Stream tool event via callback."""
        if self.tool_stream_callback:
            if asyncio.iscoroutinefunction(self.tool_stream_callback):
                await self.tool_stream_callback(event_data)
            else:
                self.tool_stream_callback(event_data)

    def _serialize_tool_message(self, tool_message):
        """Serialize tool message result for streaming."""
        try:
            if tool_message is None:
                return {"content": ["No content"], "is_error": False}

            if hasattr(tool_message, 'content'):
                return {
                    "content": [str(tool_message.content)],
                    "is_error": False,
                    "tool_call_id": getattr(tool_message, 'tool_call_id', None)
                }
            else:
                return {"content": [str(tool_message)], "is_error": False}
        except Exception as e:
            return {"content": [f"Error serializing tool message: {str(e)}"], "is_error": True}

    def _serialize_tool_result(self, result):
        """Serialize tool result for streaming."""
        try:
            if hasattr(result, 'content') and result.content:
                # Extract text content from MCP result
                content_texts = []
                for content in result.content:
                    if hasattr(content, 'text'):
                        content_texts.append(content.text)
                    elif hasattr(content, 'type') and content.type == 'text':
                        content_texts.append(str(content))

                return {
                    "content": content_texts,
                    "is_error": getattr(result, 'isError', False)
                }
            else:
                return {"content": [str(result)], "is_error": False}
        except Exception as e:
            return {"content": [f"Error serializing result: {str(e)}"], "is_error": True}


class MCPToolStreamingWrapper:
    """Wrapper for MCP agents that provides real-time streaming of tool calls."""

    def __init__(self, agent: Any, stream_callback: Callable[[Dict[str, Any]], None]):
        """
        Initialize MCP tool streaming wrapper.

        Args:
            agent: MCP agent with tool calling capabilities
            stream_callback: Callback function for streaming messages (can be async)
        """
        self.agent = agent
        self.stream_callback = stream_callback

    async def call_tool_streaming(
        self,
        tool_name: str,
        tool_args: Dict[str, Any]
    ) -> Any:
        """
        Call a tool with real-time streaming of parameters and results.

        Args:
            tool_name: Name of the tool to call
            tool_args: Arguments to pass to the tool

        Returns:
            Tool execution result
        """
        try:
            # Stream tool call start
            if self.stream_callback:
                if asyncio.iscoroutinefunction(self.stream_callback):
                    await self.stream_callback({
                        "type": "tool_call_start",
                        "tool_name": tool_name,
                        "args": tool_args,
                        "timestamp": datetime.now().isoformat()
                    })
                else:
                    self.stream_callback({
                        "type": "tool_call_start",
                        "tool_name": tool_name,
                        "args": tool_args,
                        "timestamp": datetime.now().isoformat()
                    })

            # Execute the actual tool call
            result = await self.agent.call_tool(tool_name, tool_args)

            # Stream tool call result
            if self.stream_callback:
                if asyncio.iscoroutinefunction(self.stream_callback):
                    await self.stream_callback({
                        "type": "tool_call_result",
                        "tool_name": tool_name,
                        "result": result,
                        "timestamp": datetime.now().isoformat()
                    })
                else:
                    self.stream_callback({
                        "type": "tool_call_result",
                        "tool_name": tool_name,
                        "result": result,
                        "timestamp": datetime.now().isoformat()
                    })

            return result

        except Exception as e:
            # Stream tool call error
            if self.stream_callback:
                if asyncio.iscoroutinefunction(self.stream_callback):
                    await self.stream_callback({
                        "type": "tool_call_error",
                        "tool_name": tool_name,
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    })
                else:
                    self.stream_callback({
                        "type": "tool_call_error",
                        "tool_name": tool_name,
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    })

            # Re-raise the exception to maintain error handling behavior
            raise


class FinancialSession:
    """Represents a financial analysis session with specialized agent integration."""

    def __init__(self, user_id: str, session_id: str, session_type: SessionType):
        self.user_id = user_id
        self.session_id = session_id
        self.session_type = session_type
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self.message_history = []
        self.company_name = None

        # MCP agent components
        self.mcp_app: Optional[MCPApp] = None
        self.agent_app = None

        # Specialized agents based on session type
        self.research_agent = None
        self.analyst_agent = None
        self.report_writer = None
        self.orchestrator = None

        # LLM components
        self.llm = None
        self.streaming_llm = None

        # MCP streaming components
        self.mcp_streaming_wrapper = None

    async def initialize(self, company_name: str):
        """Initialize the financial analysis session with appropriate agents."""
        try:
            self.company_name = company_name
            
            # Create MCP app for this session
            self.mcp_app = MCPApp(name=f"financial_websocket_{self.session_type.value}_{self.user_id}")

            # Start the MCP app
            self.agent_app = await self.mcp_app.run().__aenter__()

            # Get context and logger
            context = self.agent_app.context
            logger = self.agent_app.logger

            # Add current directory to filesystem server args
            context.config.mcp.servers["filesystem"].args.extend([os.getcwd()])

            # Initialize agents based on session type
            await self._initialize_agents(company_name, logger)

            logger.info(f"Financial session initialized for user {self.user_id}, type: {self.session_type.value}")

        except Exception as e:
            if self.agent_app:
                await self.agent_app.__aexit__(None, None, None)
            raise e

    async def _initialize_agents(self, company_name: str, logger):
        """Initialize appropriate agents based on session type."""

        if self.session_type == SessionType.RESEARCH:
            # Research-only session
            self.research_agent = create_research_agent(company_name)
            await self.research_agent.__aenter__()
            # Use streaming VLLM with tool call interception
            self.llm = StreamingVLLMAugmentedLLM(
                agent=self.research_agent,
                context=self.research_agent.context,
                tool_stream_callback=self._default_stream_callback,
                default_model="Qwen/Qwen3-32B"  # Explicitly set the VLLM model
            )
            # Initialize MCP streaming wrapper for tool calls
            self.mcp_streaming_wrapper = MCPToolStreamingWrapper(
                self.research_agent,
                self._default_stream_callback
            )
            # Initialize streaming LLM with MCP wrapper
            self.streaming_llm = StreamingVLLMLLM(self.llm, mcp_streaming_wrapper=self.mcp_streaming_wrapper)

        elif self.session_type == SessionType.ANALYZE:
            # Analysis-only session
            self.analyst_agent = create_analyst_agent(company_name)
            await self.analyst_agent.__aenter__()
            # Use streaming VLLM with tool call interception
            self.llm = StreamingVLLMAugmentedLLM(
                agent=self.analyst_agent,
                context=self.analyst_agent.context,
                tool_stream_callback=self._default_stream_callback,
                default_model="Qwen/Qwen3-32B"  # Explicitly set the VLLM model
            )
            # Initialize MCP streaming wrapper for tool calls
            self.mcp_streaming_wrapper = MCPToolStreamingWrapper(
                self.analyst_agent,
                self._default_stream_callback
            )
            # Initialize streaming LLM with MCP wrapper
            self.streaming_llm = StreamingVLLMLLM(self.llm, mcp_streaming_wrapper=self.mcp_streaming_wrapper)

        elif self.session_type == SessionType.REPORT:
            # Report generation session
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"{company_name.lower().replace(' ', '_')}_report_{timestamp}.md"
            output_path = os.path.join("company_reports", output_file)

            self.report_writer = create_report_writer(company_name, output_path)
            await self.report_writer.__aenter__()
            # Use streaming VLLM with tool call interception
            self.llm = StreamingVLLMAugmentedLLM(
                agent=self.report_writer,
                context=self.report_writer.context,
                tool_stream_callback=self._default_stream_callback,
                default_model="Qwen/Qwen3-32B"  # Explicitly set the VLLM model
            )
            # Initialize MCP streaming wrapper for tool calls
            self.mcp_streaming_wrapper = MCPToolStreamingWrapper(
                self.report_writer,
                self._default_stream_callback
            )
            # Initialize streaming LLM with MCP wrapper
            self.streaming_llm = StreamingVLLMLLM(self.llm, mcp_streaming_wrapper=self.mcp_streaming_wrapper)

        elif self.session_type == SessionType.FULL_ANALYSIS:
            # Full analysis with orchestrator
            await self._initialize_full_analysis(company_name, logger)

    async def _initialize_full_analysis(self, company_name: str, logger):
        """Initialize full analysis session with orchestrator."""
        # Create all agents
        self.research_agent = create_research_agent(company_name)
        
        # Research evaluator
        research_evaluator = Agent(
            name="research_evaluator",
            instruction=f"""You are an expert research evaluator specializing in financial data quality.
            
            Evaluate the research data on {company_name} based on these criteria:
            
            1. Accuracy: Are facts properly cited with source URLs? Are numbers precise?
            2. Completeness: Is all required information present? (stock price, earnings data, recent news)
            3. Specificity: Are exact figures provided rather than generalizations?
            4. Clarity: Is the information organized and easy to understand?
            
            For each criterion, provide a rating:
            - EXCELLENT: Exceeds requirements, highly reliable
            - GOOD: Meets all requirements, reliable
            - FAIR: Missing some elements but usable
            - POOR: Missing critical information, not usable
            
            Provide an overall quality rating and specific feedback on what needs improvement.
            If any critical financial data is missing (stock price, earnings figures), the overall
            rating should not exceed FAIR.""",
        )

        # Create the research quality controller - use VLLM
        research_quality_controller = EvaluatorOptimizerLLM(
            optimizer=self.research_agent,
            evaluator=research_evaluator,
            llm_factory=VLLMAugmentedLLM,
            min_rating=QualityRating.EXCELLENT,
        )

        self.analyst_agent = create_analyst_agent(company_name)

        # Create report writer with timestamped output
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"{company_name.lower().replace(' ', '_')}_report_{timestamp}.md"
        output_path = os.path.join("company_reports", output_file)
        self.report_writer = create_report_writer(company_name, output_path)

        # Create orchestrator - use VLLM
        self.orchestrator = Orchestrator(
            llm_factory=VLLMAugmentedLLM,
            available_agents=[
                research_quality_controller,
                self.analyst_agent,
                self.report_writer,
            ],
            plan_type="full",
        )

        # Create streaming LLM for full analysis
        try:
            # Try to get LLM from orchestrator or create one
            if hasattr(self.orchestrator, 'llm') and self.orchestrator.llm:
                self.llm = self.orchestrator.llm
            else:
                # Create a VLLM instance for streaming
                temp_agent = create_research_agent(company_name)
                await temp_agent.__aenter__()
                self.llm = await temp_agent.attach_llm(VLLMAugmentedLLM)
                await temp_agent.__aexit__(None, None, None)

            self.streaming_llm = StreamingVLLMLLM(self.llm)
        except Exception as e:
            logger.warning(f"Failed to create streaming LLM for full analysis: {e}")
            self.streaming_llm = None

    async def _default_stream_callback(self, message: Dict[str, Any]):
        """Default stream callback for MCP tool streaming (no-op)."""
        # This is a placeholder callback for when no specific callback is provided
        # In practice, this will be overridden by the WebSocket streaming callback
        pass

    def set_mcp_stream_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Set the stream callback for MCP tool streaming."""
        if self.mcp_streaming_wrapper:
            self.mcp_streaming_wrapper.stream_callback = callback
        # Also update the LLM's tool stream callback
        if hasattr(self.llm, 'tool_stream_callback'):
            self.llm.tool_stream_callback = callback

    async def process_message(self, message: str) -> str:
        """Process a user message through the appropriate financial analysis workflow."""
        try:
            # Update last activity
            self.last_activity = datetime.now()

            # Add to message history
            self.message_history.append(
                {
                    "role": "user",
                    "content": message,
                    "timestamp": self.last_activity.isoformat(),
                }
            )

            # Process based on session type
            response = await self._process_by_type(message)

            # Add response to history
            self.message_history.append(
                {
                    "role": "assistant",
                    "content": response,
                    "timestamp": datetime.now().isoformat(),
                }
            )

            return response

        except Exception as e:
            error_msg = f"Error processing financial analysis: {str(e)}"
            self.message_history.append(
                {
                    "role": "error",
                    "content": error_msg,
                    "timestamp": datetime.now().isoformat(),
                }
            )
            return error_msg

    async def process_message_streaming(
        self,
        message: str,
        stream_callback: Callable[[str], None]
    ) -> str:
        """Process a user message with streaming response."""
        try:
            # Update last activity
            self.last_activity = datetime.now()

            # Add to message history
            self.message_history.append(
                {
                    "role": "user",
                    "content": message,
                    "timestamp": self.last_activity.isoformat(),
                }
            )

            # Process based on session type with streaming
            response = await self._process_by_type_streaming(message, stream_callback)

            # Add response to history
            self.message_history.append(
                {
                    "role": "assistant",
                    "content": response,
                    "timestamp": datetime.now().isoformat(),
                }
            )

            return response

        except Exception as e:
            error_msg = f"Error processing financial analysis: {str(e)}"
            self.message_history.append(
                {
                    "role": "error",
                    "content": error_msg,
                    "timestamp": datetime.now().isoformat(),
                }
            )
            await stream_callback(error_msg)
            return error_msg

    async def _process_by_type(self, message: str) -> str:
        """Process message based on session type."""

        if self.session_type == SessionType.RESEARCH:
            if not self.llm:
                return "Error: Research agent not initialized"
            return await self.llm.generate_str(message=message)

        elif self.session_type == SessionType.ANALYZE:
            if not self.llm:
                return "Error: Analyst agent not initialized"
            return await self.llm.generate_str(message=message)

        elif self.session_type == SessionType.REPORT:
            if not self.llm:
                return "Error: Report writer not initialized"
            return await self.llm.generate_str(message=message)

        elif self.session_type == SessionType.FULL_ANALYSIS:
            if not self.orchestrator:
                return "Error: Orchestrator not initialized"

            # Create comprehensive analysis task
            task = f"""Create a high-quality stock analysis report for {self.company_name} by following these steps:

            1. Use the EvaluatorOptimizerLLM component (named 'research_quality_controller') to gather high-quality
               financial data about {self.company_name}. This component will automatically evaluate
               and improve the research until it reaches EXCELLENT quality.

               Ask for:
               - Current stock price and recent movement
               - Latest quarterly earnings results and performance vs expectations
               - Recent news and developments

            2. Use the financial_analyst to analyze this research data and identify key insights.

            3. Use the report_writer to create a comprehensive stock report.

            The final report should be professional, fact-based, and include all relevant financial information.

            User request: {message}"""

            return await self.orchestrator.generate_str(
                message=task,
                request_params=RequestParams(model="Qwen/Qwen3-32B")
            )

    async def _process_by_type_streaming(
        self,
        message: str,
        stream_callback: Callable[[str], None]
    ) -> str:
        """Process message based on session type with streaming responses."""

        if self.session_type == SessionType.RESEARCH:
            if not self.streaming_llm:
                error_msg = "Error: Research agent streaming not initialized"
                await stream_callback(error_msg)
                return error_msg

            return await self.streaming_llm.generate_str_streaming(
                message=message,
                stream_callback=stream_callback
            )

        elif self.session_type == SessionType.ANALYZE:
            if not self.streaming_llm:
                error_msg = "Error: Analyst agent streaming not initialized"
                await stream_callback(error_msg)
                return error_msg

            return await self.streaming_llm.generate_str_streaming(
                message=message,
                stream_callback=stream_callback
            )

        elif self.session_type == SessionType.REPORT:
            if not self.streaming_llm:
                error_msg = "Error: Report writer streaming not initialized"
                await stream_callback(error_msg)
                return error_msg

            return await self.streaming_llm.generate_str_streaming(
                message=message,
                stream_callback=stream_callback
            )

        elif self.session_type == SessionType.FULL_ANALYSIS:
            if not self.orchestrator or not self.streaming_llm:
                error_msg = "Error: Full analysis streaming not initialized"
                await stream_callback(error_msg)
                return error_msg

            # For full analysis, we'll use a combination of streaming and non-streaming
            # First, send a message that we're starting the analysis
            await stream_callback("Starting comprehensive financial analysis for " + self.company_name + "...\n\n")

            # Create comprehensive analysis task
            task = f"""Create a high-quality stock analysis report for {self.company_name} by following these steps:

            1. Use the EvaluatorOptimizerLLM component (named 'research_quality_controller') to gather high-quality
               financial data about {self.company_name}. This component will automatically evaluate
               and improve the research until it reaches EXCELLENT quality.

               Ask for:
               - Current stock price and recent movement
               - Latest quarterly earnings results and performance vs expectations
               - Recent news and developments

            2. Use the financial_analyst to analyze this research data and identify key insights.

            3. Use the report_writer to create a comprehensive stock report.

            The final report should be professional, fact-based, and include all relevant financial information.

            User request: {message}"""

            # For orchestrator, we can't easily stream, so we'll provide progress updates
            await stream_callback("Researching financial data...\n")

            # Use the orchestrator (non-streaming)
            result = await self.orchestrator.generate_str(
                message=task,
                request_params=RequestParams(model="Qwen/Qwen3-32B")
            )

            # Send the final result
            await stream_callback("\n\nFinal Analysis Report:\n\n")
            await stream_callback(result)

            return result

    async def cleanup(self):
        """Clean up the session resources."""
        try:
            if self.research_agent:
                await self.research_agent.__aexit__(None, None, None)
            if self.analyst_agent:
                await self.analyst_agent.__aexit__(None, None, None)
            if self.report_writer:
                await self.report_writer.__aexit__(None, None, None)
            if self.agent_app:
                await self.agent_app.__aexit__(None, None, None)
        except Exception as e:
            print(f"Error during session cleanup for user {self.user_id}: {e}")


class FinancialSessionManager:
    """Manages financial analysis sessions for the WebSocket server."""

    def __init__(self):
        self.sessions: Dict[str, FinancialSession] = {}
        self.cleanup_interval = 3600  # Clean up inactive sessions every hour
        self.max_inactive_time = 7200  # Remove sessions inactive for 2 hours

    async def initialize(self):
        """Initialize the session manager."""
        # Start cleanup task
        asyncio.create_task(self._cleanup_task())
        
        # Ensure output directory exists
        os.makedirs("company_reports", exist_ok=True)

    async def get_or_create_session(
        self, 
        user_id: str, 
        session_type: SessionType, 
        company_name: str
    ) -> FinancialSession:
        """Get existing session or create a new one for the user."""
        session_key = f"{user_id}_{session_type.value}"
        
        if session_key in self.sessions:
            session = self.sessions[session_key]
            session.last_activity = datetime.now()
            return session

        # Create new session
        session_id = str(uuid.uuid4())
        session = FinancialSession(user_id, session_id, session_type)

        try:
            await session.initialize(company_name)
            self.sessions[session_key] = session
            return session
        except Exception as e:
            await session.cleanup()
            raise Exception(f"Failed to create {session_type.value} session for user {user_id}: {str(e)}")

    async def cleanup_session(self, user_id: str, session_type: SessionType):
        """Clean up a specific user session."""
        session_key = f"{user_id}_{session_type.value}"
        if session_key in self.sessions:
            session = self.sessions[session_key]
            await session.cleanup()
            del self.sessions[session_key]

    async def cleanup(self):
        """Clean up all sessions."""
        cleanup_tasks = []
        for session_key, session in self.sessions.items():
            cleanup_tasks.append(session.cleanup())

        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)

        self.sessions.clear()

    async def _cleanup_task(self):
        """Background task to clean up inactive sessions."""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)

                current_time = datetime.now()
                inactive_sessions = []

                for session_key, session in self.sessions.items():
                    time_since_activity = (
                        current_time - session.last_activity
                    ).total_seconds()
                    if time_since_activity > self.max_inactive_time:
                        inactive_sessions.append(session_key)

                # Clean up inactive sessions
                for session_key in inactive_sessions:
                    print(f"Cleaning up inactive session: {session_key}")
                    session = self.sessions[session_key]
                    await session.cleanup()
                    del self.sessions[session_key]

            except Exception as e:
                print(f"Error in cleanup task: {e}")

    def get_session_info(self, user_id: str, session_type: SessionType) -> Optional[dict]:
        """Get session information for a user and session type."""
        session_key = f"{user_id}_{session_type.value}"
        if session_key not in self.sessions:
            return None

        session = self.sessions[session_key]
        return {
            "user_id": session.user_id,
            "session_id": session.session_id,
            "session_type": session.session_type.value,
            "company_name": session.company_name,
            "created_at": session.created_at.isoformat(),
            "last_activity": session.last_activity.isoformat(),
            "message_count": len(session.message_history),
        }
