#!/usr/bin/env python3
"""
Comprehensive test suite for WebSocket streaming with tool calls integration.
Tests both the financial analyzer and basic WebSocket implementations.
"""

import asyncio
import json
import pytest
import websockets
import time
from typing import List, Dict, Any
from datetime import datetime
from urllib.parse import quote


class WebSocketStreamingTestSuite:
    """Test suite for validating WebSocket streaming with tool calls."""

    def __init__(self):
        self.base_url = "ws://localhost:8000"
        self.test_user_id = "test_user_streaming"
        self.test_company = "Apple Inc."

    async def test_basic_websocket_streaming(self) -> bool:
        """Test research WebSocket streaming implementation."""
        print("🧪 Testing Research WebSocket Streaming...")

        try:
            uri = f"{self.base_url}/ws/research/{self.test_user_id}?company={quote(self.test_company)}"
            
            async with websockets.connect(uri) as websocket:
                # Test streaming message
                test_message = {
                    "message": f"Research the current stock price of {self.test_company}",
                    "streaming": True
                }
                
                await websocket.send(json.dumps(test_message))
                
                # Collect streaming events
                events = []
                stream_started = False
                stream_ended = False
                tool_events = []
                
                while True:
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                        data = json.loads(response)
                        events.append(data)
                        
                        if data["type"] == "stream_start":
                            stream_started = True
                            print("✅ Stream started")
                            
                        elif data["type"] == "stream_chunk":
                            print(f"📝 Chunk: {data['message'][:50]}...")
                            
                        elif data["type"] == "tool_stream":
                            tool_events.append(data["data"])
                            print(f"🔧 Tool event: {data['data']['type']}")
                            
                        elif data["type"] == "stream_end":
                            stream_ended = True
                            print("✅ Stream ended")
                            break
                            
                    except asyncio.TimeoutError:
                        print("⏰ Timeout waiting for response")
                        break
                
                # Validate results
                assert stream_started, "Stream should have started"
                assert stream_ended, "Stream should have ended"
                assert len(events) > 0, "Should have received events"
                
                # Check for tool events (filesystem listing should trigger tools)
                if len(tool_events) > 0:
                    print(f"✅ Tool events detected: {len(tool_events)}")
                    
                    # Validate tool event structure
                    for event in tool_events:
                        assert "type" in event, "Tool event should have type"
                        assert "timestamp" in event, "Tool event should have timestamp"
                        
                        if event["type"] == "tool_call_start":
                            assert "tool_name" in event, "Tool start should have tool_name"
                            assert "args" in event, "Tool start should have args"
                            
                        elif event["type"] == "tool_call_result":
                            assert "tool_name" in event, "Tool result should have tool_name"
                            assert "result" in event, "Tool result should have result"
                            assert "execution_time" in event, "Tool result should have execution_time"
                
                print("✅ Basic WebSocket streaming test passed")
                return True
                
        except Exception as e:
            print(f"❌ Basic WebSocket streaming test failed: {e}")
            return False

    async def test_financial_websocket_streaming(self) -> bool:
        """Test financial analyzer WebSocket streaming implementation."""
        print("🧪 Testing Financial WebSocket Streaming...")
        
        try:
            uri = f"{self.base_url}/ws/analyze/{self.test_user_id}?company={quote(self.test_company)}"
            
            async with websockets.connect(uri) as websocket:
                # Test streaming message
                test_message = {
                    "message": f"Research the current stock price of {self.test_company}",
                    "streaming": True
                }
                
                await websocket.send(json.dumps(test_message))
                
                # Collect streaming events
                events = []
                stream_started = False
                stream_ended = False
                mcp_tool_events = []
                
                while True:
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                        data = json.loads(response)
                        events.append(data)
                        
                        if data["type"] == "stream_start":
                            stream_started = True
                            print("✅ Financial stream started")
                            
                        elif data["type"] == "stream_chunk":
                            print(f"📝 Financial chunk: {data['message'][:50]}...")
                            
                        elif data["type"] == "mcp_tool_stream":
                            mcp_tool_events.append(data["data"])
                            print(f"🔧 MCP tool event: {data['data']['type']}")
                            
                        elif data["type"] == "stream_end":
                            stream_ended = True
                            print("✅ Financial stream ended")
                            break
                            
                    except asyncio.TimeoutError:
                        print("⏰ Timeout waiting for financial response")
                        break
                
                # Validate results
                assert stream_started, "Financial stream should have started"
                assert stream_ended, "Financial stream should have ended"
                assert len(events) > 0, "Should have received financial events"
                
                # Check for MCP tool events (research should trigger web searches)
                if len(mcp_tool_events) > 0:
                    print(f"✅ MCP tool events detected: {len(mcp_tool_events)}")
                    
                    # Validate MCP tool event structure
                    for event in mcp_tool_events:
                        assert "type" in event, "MCP tool event should have type"
                        assert "timestamp" in event, "MCP tool event should have timestamp"
                        
                        if event["type"] == "tool_call_start":
                            assert "tool_name" in event, "MCP tool start should have tool_name"
                            
                        elif event["type"] == "tool_call_result":
                            assert "tool_name" in event, "MCP tool result should have tool_name"
                            assert "execution_time" in event, "MCP tool result should have execution_time"
                
                print("✅ Financial WebSocket streaming test passed")
                return True
                
        except Exception as e:
            print(f"❌ Financial WebSocket streaming test failed: {e}")
            return False

    async def test_streaming_latency(self) -> bool:
        """Test that streaming maintains <100ms latency requirements."""
        print("🧪 Testing Streaming Latency...")
        
        try:
            uri = f"{self.base_url}/ws/research/{self.test_user_id}?company={quote(self.test_company)}"

            async with websockets.connect(uri) as websocket:
                # Test simple message for latency
                test_message = {
                    "message": f"What is the current stock price of {self.test_company}?",
                    "streaming": True
                }
                
                start_time = time.time()
                await websocket.send(json.dumps(test_message))
                
                # Measure time to first chunk
                first_chunk_time = None
                
                while True:
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                        data = json.loads(response)
                        
                        if data["type"] == "stream_chunk" and first_chunk_time is None:
                            first_chunk_time = time.time()
                            latency = (first_chunk_time - start_time) * 1000  # Convert to ms
                            print(f"⏱️ First chunk latency: {latency:.2f}ms")
                            
                            # Validate latency requirement
                            assert latency < 100, f"Latency {latency:.2f}ms exceeds 100ms requirement"
                            break
                            
                        elif data["type"] == "stream_end":
                            break
                            
                    except asyncio.TimeoutError:
                        print("⏰ Timeout waiting for latency test response")
                        return False
                
                print("✅ Streaming latency test passed")
                return True
                
        except Exception as e:
            print(f"❌ Streaming latency test failed: {e}")
            return False

    async def test_backward_compatibility(self) -> bool:
        """Test that non-streaming mode still works (backward compatibility)."""
        print("🧪 Testing Backward Compatibility...")
        
        try:
            uri = f"{self.base_url}/ws/research/{self.test_user_id}?company={quote(self.test_company)}"

            async with websockets.connect(uri) as websocket:
                # Test non-streaming message
                test_message = {
                    "message": f"What is the ticker symbol for {self.test_company}?",
                    "streaming": False
                }
                
                await websocket.send(json.dumps(test_message))
                
                # Should receive a single result message
                response = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                data = json.loads(response)
                
                assert data["type"] == "result", "Should receive result type for non-streaming"
                assert "message" in data, "Result should contain message"
                assert data["user_id"] == self.test_user_id, "Should have correct user_id"
                
                print("✅ Backward compatibility test passed")
                return True
                
        except Exception as e:
            print(f"❌ Backward compatibility test failed: {e}")
            return False

    async def run_all_tests(self) -> bool:
        """Run all WebSocket streaming tests."""
        print("🚀 Starting WebSocket Streaming Integration Tests")
        print("=" * 60)
        
        tests = [
            ("Research WebSocket Streaming", self.test_basic_websocket_streaming),
            ("Financial WebSocket Streaming", self.test_financial_websocket_streaming),
            ("Streaming Latency", self.test_streaming_latency),
            ("Backward Compatibility", self.test_backward_compatibility),
        ]
        
        results = []
        
        for test_name, test_func in tests:
            print(f"\n📋 Running: {test_name}")
            try:
                result = await test_func()
                results.append((test_name, result))
                if result:
                    print(f"✅ {test_name}: PASSED")
                else:
                    print(f"❌ {test_name}: FAILED")
            except Exception as e:
                print(f"❌ {test_name}: ERROR - {e}")
                results.append((test_name, False))
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 Test Results Summary:")
        passed = sum(1 for _, result in results if result)
        total = len(results)
        
        for test_name, result in results:
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"  {test_name}: {status}")
        
        print(f"\n🎯 Overall: {passed}/{total} tests passed")
        
        return passed == total


async def main():
    """Run the WebSocket streaming integration tests."""
    suite = WebSocketStreamingTestSuite()
    success = await suite.run_all_tests()
    
    if success:
        print("\n🎉 All WebSocket streaming tests passed!")
        return 0
    else:
        print("\n💥 Some WebSocket streaming tests failed!")
        return 1


async def performance_benchmark():
    """Run performance benchmarks for streaming latency."""
    print("🏃‍♂️ Running Performance Benchmarks")
    print("=" * 60)

    suite = WebSocketStreamingTestSuite()

    # Test multiple message types for latency
    test_messages = [
        "Hello",
        "What is 2+2?",
        "List files in current directory",
        "Tell me about artificial intelligence",
        "Search for recent news about technology"
    ]

    latencies = []

    for i, message in enumerate(test_messages, 1):
        print(f"\n📊 Benchmark {i}/{len(test_messages)}: '{message[:30]}...'")

        try:
            uri = f"{suite.base_url}/ws/{suite.test_user_id}"

            async with websockets.connect(uri) as websocket:
                test_message = {"message": message, "streaming": True}

                start_time = time.time()
                await websocket.send(json.dumps(test_message))

                # Measure time to first chunk
                while True:
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        data = json.loads(response)

                        if data["type"] == "stream_chunk":
                            first_chunk_time = time.time()
                            latency = (first_chunk_time - start_time) * 1000
                            latencies.append(latency)
                            print(f"   ⏱️ Latency: {latency:.2f}ms")
                            break
                        elif data["type"] == "stream_end":
                            break

                    except asyncio.TimeoutError:
                        print("   ⏰ Timeout - no chunks received")
                        break

        except Exception as e:
            print(f"   ❌ Benchmark failed: {e}")

    # Performance summary
    if latencies:
        avg_latency = sum(latencies) / len(latencies)
        min_latency = min(latencies)
        max_latency = max(latencies)

        print(f"\n📈 Performance Summary:")
        print(f"   Average Latency: {avg_latency:.2f}ms")
        print(f"   Min Latency: {min_latency:.2f}ms")
        print(f"   Max Latency: {max_latency:.2f}ms")
        print(f"   Target: <100ms")

        if avg_latency < 100:
            print("   ✅ Performance target met!")
        else:
            print("   ❌ Performance target not met!")

        return avg_latency < 100
    else:
        print("   ❌ No latency measurements collected")
        return False


if __name__ == "__main__":
    import sys
    import argparse

    parser = argparse.ArgumentParser(description="WebSocket Streaming Integration Tests")
    parser.add_argument("--benchmark", action="store_true", help="Run performance benchmarks")
    args = parser.parse_args()

    if args.benchmark:
        success = asyncio.run(performance_benchmark())
        sys.exit(0 if success else 1)
    else:
        sys.exit(asyncio.run(main()))
