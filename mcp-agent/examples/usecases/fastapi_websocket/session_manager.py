import asyncio
import os
import uuid
import json
from typing import Dict, Optional, Callable, Any
from datetime import datetime

from mcp_agent.app import <PERSON><PERSON><PERSON>
from mcp_agent.agents.agent import Agent
from mcp_agent.workflows.llm.augmented_llm_openai import OpenAIAugmentedLLM
from mcp_agent.workflows.llm.augmented_llm_vllm import VLLMAug<PERSON><PERSON><PERSON>
from mcp_agent.workflows.llm.augmented_llm import CallToolRequest, RequestParams
from openai.types.chat import ChatCompletionMessageToolCall


class StreamingVLLMAugmentedLLM(VLLMAugmentedLLM):
    """Custom VLLMAugmentedLLM that intercepts and streams tool calls in real-time."""

    def __init__(self, *args, tool_stream_callback=None, **kwargs):
        """Initialize with optional tool streaming callback."""
        super().__init__(*args, **kwargs)
        self.tool_stream_callback = tool_stream_callback

    async def select_model(self, request_params=None):
        """Override model selection to ensure we use the VLLM model."""
        # Always return the VLLM model
        return "Qwen/Qwen3-32B"

    async def call_tool(
        self,
        request: CallToolRequest,
        tool_call_id: str | None = None,
    ):
        """Override call_tool to intercept and stream tool calls."""
        tool_name = request.params.name
        tool_args = request.params.arguments

        # Stream tool call start
        if self.tool_stream_callback:
            await self._stream_tool_event({
                "type": "tool_call_start",
                "tool_name": tool_name,
                "args": tool_args,
                "tool_call_id": tool_call_id,
                "timestamp": datetime.now().isoformat()
            })

        start_time = datetime.now()

        try:
            # Execute the actual tool call
            result = await super().call_tool(request, tool_call_id)

            execution_time = (datetime.now() - start_time).total_seconds()

            # Stream tool call result
            if self.tool_stream_callback:
                await self._stream_tool_event({
                    "type": "tool_call_result",
                    "tool_name": tool_name,
                    "result": self._serialize_tool_result(result),
                    "execution_time": execution_time,
                    "tool_call_id": tool_call_id,
                    "timestamp": datetime.now().isoformat()
                })

            return result

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()

            # Stream tool call error
            if self.tool_stream_callback:
                await self._stream_tool_event({
                    "type": "tool_call_error",
                    "tool_name": tool_name,
                    "error": str(e),
                    "execution_time": execution_time,
                    "tool_call_id": tool_call_id,
                    "timestamp": datetime.now().isoformat()
                })

            raise

    async def execute_tool_call(self, tool_call):
        """Override execute_tool_call to intercept tool calls at the execution level."""
        tool_name = tool_call.function.name
        tool_args_str = tool_call.function.arguments
        tool_call_id = tool_call.id

        # Parse tool arguments
        tool_args = {}
        try:
            if tool_args_str:
                import json
                tool_args = json.loads(tool_args_str)
        except json.JSONDecodeError:
            tool_args = {"raw_arguments": tool_args_str}

        # Stream tool call start
        if self.tool_stream_callback:
            await self._stream_tool_event({
                "type": "tool_call_start",
                "tool_name": tool_name,
                "args": tool_args,
                "tool_call_id": tool_call_id,
                "timestamp": datetime.now().isoformat()
            })

        start_time = datetime.now()

        try:
            # Execute the actual tool call via parent method
            result = await super().execute_tool_call(tool_call)

            execution_time = (datetime.now() - start_time).total_seconds()

            # Stream tool call result
            if self.tool_stream_callback:
                await self._stream_tool_event({
                    "type": "tool_call_result",
                    "tool_name": tool_name,
                    "result": self._serialize_tool_message(result),
                    "execution_time": execution_time,
                    "tool_call_id": tool_call_id,
                    "timestamp": datetime.now().isoformat()
                })

            return result

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()

            # Stream tool call error
            if self.tool_stream_callback:
                await self._stream_tool_event({
                    "type": "tool_call_error",
                    "tool_name": tool_name,
                    "error": str(e),
                    "execution_time": execution_time,
                    "tool_call_id": tool_call_id,
                    "timestamp": datetime.now().isoformat()
                })

            raise

    async def _stream_tool_event(self, event_data):
        """Stream tool event via callback."""
        if self.tool_stream_callback:
            if asyncio.iscoroutinefunction(self.tool_stream_callback):
                await self.tool_stream_callback(event_data)
            else:
                self.tool_stream_callback(event_data)

    def _serialize_tool_message(self, tool_message):
        """Serialize tool message result for streaming."""
        try:
            if tool_message is None:
                return {"content": ["No content"], "is_error": False}

            if hasattr(tool_message, 'content'):
                return {
                    "content": [str(tool_message.content)],
                    "is_error": False,
                    "tool_call_id": getattr(tool_message, 'tool_call_id', None)
                }
            else:
                return {"content": [str(tool_message)], "is_error": False}
        except Exception as e:
            return {"content": [f"Error serializing tool message: {str(e)}"], "is_error": True}

    def _serialize_tool_result(self, result):
        """Serialize tool result for streaming."""
        try:
            if hasattr(result, 'content') and result.content:
                # Extract text content from MCP result
                content_texts = []
                for content in result.content:
                    if hasattr(content, 'text'):
                        content_texts.append(content.text)
                    elif hasattr(content, 'type') and content.type == 'text':
                        content_texts.append(str(content))

                return {
                    "content": content_texts,
                    "is_error": getattr(result, 'isError', False)
                }
            else:
                return {"content": [str(result)], "is_error": False}
        except Exception as e:
            return {"content": [f"Error serializing result: {str(e)}"], "is_error": True}


class StreamingVLLMLLM:
    """Wrapper for VLLMAugmentedLLM that provides real streaming capabilities."""

    def __init__(self, llm: VLLMAugmentedLLM):
        self.llm = llm

    async def generate_str_streaming(
        self,
        message: str,
        request_params: RequestParams | None = None,
        stream_callback: Callable[[str], None] | None = None,
        **kwargs
    ) -> str:
        """Generate a streaming response using VLLMAugmentedLLM with tool calling support."""
        try:
            if stream_callback:
                await stream_callback("Initializing analysis...\n\n")

            # Use VLLMAugmentedLLM.generate method to get proper tool calling support
            # This method handles tool retrieval, formatting, and execution
            responses = await self.llm.generate(
                message=message,
                request_params=request_params,
                enable_thinking=True
            )

            # Extract content from responses and stream it
            full_response = ""

            for response in responses:
                content = response.content
                if not content:
                    continue

                if isinstance(content, str):
                    full_response += content
                    if stream_callback:
                        # Stream the content in chunks for better UX
                        words = content.split()
                        for i in range(0, len(words), 3):  # 3 words per chunk
                            chunk = " ".join(words[i:i+3]) + " "
                            await stream_callback(chunk)
                            await asyncio.sleep(0.02)  # Small delay for streaming effect

            return full_response

        except Exception as e:
            # Handle errors gracefully
            error_msg = f"Error processing request: {str(e)}"
            if stream_callback:
                await stream_callback(error_msg)
            return error_msg


class UserSession:
    """Represents a user session with MCP agent integration and streaming capabilities."""

    def __init__(self, user_id: str, session_id: str):
        self.user_id = user_id
        self.session_id = session_id
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self.message_history = []

        # MCP agent components
        self.mcp_app: Optional[MCPApp] = None
        self.agent_app = None
        self.agent: Optional[Agent] = None
        self.llm = None
        self.streaming_llm = None

        # Tool streaming callback
        self.tool_stream_callback = None

    async def initialize(self):
        """Initialize the MCP agent for this session."""
        try:
            # Create MCP app for this session
            self.mcp_app = MCPApp(name=f"mcp_websocket_session_{self.user_id}")

            # Start the MCP app
            self.agent_app = await self.mcp_app.run().__aenter__()

            # Get context and logger
            context = self.agent_app.context
            logger = self.agent_app.logger

            # Add current directory to filesystem server args
            context.config.mcp.servers["filesystem"].args.extend([os.getcwd()])

            # Create agent with access to filesystem and fetch servers
            self.agent = Agent(
                name=f"websocket_agent_{self.user_id}",
                instruction=f"""You are an AI assistant for user {self.user_id} with access to filesystem and web resources.
                You can help with file operations, web searches, and general assistance.
                Always be helpful, accurate, and concise in your responses.""",
                server_names=["fetch", "filesystem"],
            )

            # Initialize the agent
            await self.agent.__aenter__()

            # Create streaming LLM with tool call interception
            self.llm = StreamingVLLMAugmentedLLM(
                agent=self.agent,
                context=self.agent.context,
                tool_stream_callback=self._default_stream_callback,
                default_model="Qwen/Qwen3-32B"  # Explicitly set the VLLM model
            )

            # Initialize streaming wrapper
            self.streaming_llm = StreamingVLLMLLM(self.llm)

            logger.info(f"Session initialized for user {self.user_id} with streaming capabilities")

        except Exception as e:
            if self.agent_app:
                await self.agent_app.__aexit__(None, None, None)
            raise e

    async def _default_stream_callback(self, message: Dict[str, Any]):
        """Default stream callback for tool streaming (no-op)."""
        # This is a placeholder callback for when no specific callback is provided
        # In practice, this will be overridden by the WebSocket streaming callback
        pass

    def set_tool_stream_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Set the stream callback for tool streaming."""
        self.tool_stream_callback = callback
        # Also update the LLM's tool stream callback
        if hasattr(self.llm, 'tool_stream_callback'):
            self.llm.tool_stream_callback = callback

    async def process_message(self, message: str) -> str:
        """Process a user message through the MCP agent."""
        try:
            # Update last activity
            self.last_activity = datetime.now()

            # Add to message history
            self.message_history.append(
                {
                    "role": "user",
                    "content": message,
                    "timestamp": self.last_activity.isoformat(),
                }
            )

            # Process through LLM
            if not self.llm:
                return "Error: Agent not initialized"

            response = await self.llm.generate_str(message=message)

            # Add response to history
            self.message_history.append(
                {
                    "role": "assistant",
                    "content": response,
                    "timestamp": datetime.now().isoformat(),
                }
            )

            return response

        except Exception as e:
            error_msg = f"Error processing message: {str(e)}"
            self.message_history.append(
                {
                    "role": "error",
                    "content": error_msg,
                    "timestamp": datetime.now().isoformat(),
                }
            )
            return error_msg

    async def process_message_streaming(
        self,
        message: str,
        stream_callback: Callable[[str], None]
    ) -> str:
        """Process a user message with streaming response."""
        try:
            # Update last activity
            self.last_activity = datetime.now()

            # Add to message history
            self.message_history.append(
                {
                    "role": "user",
                    "content": message,
                    "timestamp": self.last_activity.isoformat(),
                }
            )

            # Process through streaming LLM
            if not self.streaming_llm:
                error_msg = "Error: Streaming agent not initialized"
                await stream_callback(error_msg)
                return error_msg

            response = await self.streaming_llm.generate_str_streaming(
                message=message,
                stream_callback=stream_callback
            )

            # Add response to history
            self.message_history.append(
                {
                    "role": "assistant",
                    "content": response,
                    "timestamp": datetime.now().isoformat(),
                }
            )

            return response

        except Exception as e:
            error_msg = f"Error processing streaming message: {str(e)}"
            self.message_history.append(
                {
                    "role": "error",
                    "content": error_msg,
                    "timestamp": datetime.now().isoformat(),
                }
            )
            await stream_callback(error_msg)
            return error_msg

    async def cleanup(self):
        """Clean up the session resources."""
        try:
            if self.agent:
                await self.agent.__aexit__(None, None, None)
            if self.agent_app:
                await self.agent_app.__aexit__(None, None, None)
        except Exception as e:
            print(f"Error during session cleanup for user {self.user_id}: {e}")


class SessionManager:
    """Manages user sessions for the WebSocket server."""

    def __init__(self):
        self.sessions: Dict[str, UserSession] = {}
        self.cleanup_interval = 3600  # Clean up inactive sessions every hour
        self.max_inactive_time = 7200  # Remove sessions inactive for 2 hours

    async def initialize(self):
        """Initialize the session manager."""
        # Start cleanup task
        asyncio.create_task(self._cleanup_task())

    async def get_or_create_session(self, user_id: str) -> UserSession:
        """Get existing session or create a new one for the user."""
        if user_id in self.sessions:
            session = self.sessions[user_id]
            session.last_activity = datetime.now()
            return session

        # Create new session
        session_id = str(uuid.uuid4())
        session = UserSession(user_id, session_id)

        try:
            await session.initialize()
            self.sessions[user_id] = session
            return session
        except Exception as e:
            await session.cleanup()
            raise Exception(f"Failed to create session for user {user_id}: {str(e)}")

    async def cleanup_session(self, user_id: str):
        """Clean up a specific user session."""
        if user_id in self.sessions:
            session = self.sessions[user_id]
            await session.cleanup()
            del self.sessions[user_id]

    async def cleanup(self):
        """Clean up all sessions."""
        cleanup_tasks = []
        for user_id, session in self.sessions.items():
            cleanup_tasks.append(session.cleanup())

        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)

        self.sessions.clear()

    async def _cleanup_task(self):
        """Background task to clean up inactive sessions."""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)

                current_time = datetime.now()
                inactive_users = []

                for user_id, session in self.sessions.items():
                    time_since_activity = (
                        current_time - session.last_activity
                    ).total_seconds()
                    if time_since_activity > self.max_inactive_time:
                        inactive_users.append(user_id)

                # Clean up inactive sessions
                for user_id in inactive_users:
                    print(f"Cleaning up inactive session for user: {user_id}")
                    await self.cleanup_session(user_id)

            except Exception as e:
                print(f"Error in cleanup task: {e}")

    def get_session_info(self, user_id: str) -> Optional[dict]:
        """Get session information for a user."""
        if user_id not in self.sessions:
            return None

        session = self.sessions[user_id]
        return {
            "user_id": session.user_id,
            "session_id": session.session_id,
            "created_at": session.created_at.isoformat(),
            "last_activity": session.last_activity.isoformat(),
            "message_count": len(session.message_history),
        }
