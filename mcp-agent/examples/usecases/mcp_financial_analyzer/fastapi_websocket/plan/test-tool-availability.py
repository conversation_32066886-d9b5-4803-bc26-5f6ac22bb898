#!/usr/bin/env python3
"""
Test to verify that MCP tools are available and accessible to the research agent.
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from session_manager import FinancialSession, SessionType


async def test_tool_availability():
    """Test that research agent has access to MCP tools."""
    
    print("🔧 Testing MCP tool availability...")
    
    try:
        # Create a research session
        session = FinancialSession("tool_test_user", "tool_test_session", SessionType.RESEARCH)
        print("✅ Session object created")
        
        # Initialize session
        print("🔄 Initializing session...")
        await session.initialize("Apple Inc.")
        print("✅ Session initialized successfully")
        
        # Check if research agent has tools
        print("🛠️ Checking available tools...")
        
        if hasattr(session.research_agent, 'list_tools'):
            tools = await session.research_agent.list_tools()
            print(f"📋 Available tools result: {type(tools)}")

            if tools:
                print("🔍 Tool details:")
                if hasattr(tools, 'tools') and tools.tools:
                    for tool in tools.tools:
                        print(f"   - {tool.name}: {tool.description}")
                else:
                    print(f"   Raw tools object: {tools}")
            else:
                print("⚠️ No tools found")
        else:
            print("❌ list_tools method not available")
        
        # Check server capabilities
        print("\n🌐 Checking server capabilities...")
        
        if hasattr(session.research_agent, 'list_capabilities'):
            capabilities = await session.research_agent.list_capabilities()
            print(f"📋 Server capabilities result: {type(capabilities)}")

            if capabilities:
                print("🔍 Capability details:")
                if isinstance(capabilities, dict):
                    for server_name, caps in capabilities.items():
                        print(f"   Server: {server_name}")
                        if hasattr(caps, 'tools') and caps.tools:
                            print(f"     Tools: {[tool.name for tool in caps.tools]}")
                        else:
                            print(f"     Tools: {caps}")
                else:
                    print(f"   Raw capabilities: {capabilities}")
            else:
                print("⚠️ No capabilities found")
        else:
            print("❌ list_capabilities method not available")
        
        # Test direct tool call
        print("\n🔧 Testing direct tool call...")
        
        try:
            # Try to call a simple tool directly
            if hasattr(session.research_agent, 'call_tool'):
                # Test g-search tool
                print("🔍 Testing g-search tool...")
                result = await session.research_agent.call_tool("g-search", {"query": "Apple Inc. test"})
                print(f"✅ g-search tool call successful: {str(result)[:100]}...")
            else:
                print("❌ call_tool method not available")
                
        except Exception as e:
            print(f"⚠️ Direct tool call failed: {e}")
        
        # Test LLM with explicit tool instruction
        print("\n🤖 Testing LLM with explicit tool instruction...")
        
        try:
            # Create a message that should definitely trigger tool usage
            explicit_message = """You MUST use the g-search tool to search for "Apple Inc. stock price today". 
            Do not generate a response from your training data. 
            Use the search tool and return the actual search results."""
            
            print(f"📤 Sending explicit tool instruction...")
            response = await session.process_message(explicit_message)
            print(f"📥 Response length: {len(response)} characters")
            print(f"📄 Response preview: {response[:200]}...")
            
            # Check if response contains indicators of tool usage
            tool_indicators = ["search", "found", "results", "according to", "source"]
            indicators_found = [ind for ind in tool_indicators if ind.lower() in response.lower()]
            print(f"🔍 Tool usage indicators found: {indicators_found}")
            
        except Exception as e:
            print(f"❌ LLM test failed: {e}")
        
        # Cleanup
        await session.cleanup()
        print("✅ Session cleaned up")
        
        return True
        
    except Exception as e:
        print(f"❌ Tool availability test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run the tool availability test."""
    print("🚀 MCP Tool Availability Test")
    print("=" * 50)
    
    success = await test_tool_availability()
    
    if success:
        print("\n🎉 Tool availability test completed!")
    else:
        print("\n❌ Tool availability test failed!")
    
    return success


if __name__ == "__main__":
    asyncio.run(main())
